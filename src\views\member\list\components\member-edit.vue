<!-- 用户编辑弹窗 -->
<template>
    <ele-modal
            width="1200px"
            :visible="visible"
            :append-to-body="true"
            :close-on-click-modal="false"
            custom-class="ele-dialog-form"
            :title="isUpdate ? '修改会员' : '添加会员'"
            @update:visible="updateVisible"
    >
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">

            <el-form-item label="会员名称:" prop="name">
                <el-input
                        v-model="form.name"
                        placeholder="请输入会员名称"
                />
            </el-form-item>

            <el-form-item label="原价:" prop="price">
                <el-input
                        v-model="form.price"
                        placeholder="请输入原价"
                />
            </el-form-item>


            <el-form-item label="价格:" prop="money">
                <el-input
                        v-model="form.money"
                        placeholder="请输入价格"
                />
            </el-form-item>


            <el-form-item label="会员天数:" prop="day">
                <el-input-number
                        v-model="form.day"
                        placeholder="请输入会员天数"
                />
            </el-form-item>


            <el-form-item label="视频合成无限:" prop="second_infinite">
                <el-radio-group v-model="form.second_infinite" size="small" @change="updateSecond">
                    <el-radio-button :label=0 :value=0>否</el-radio-button>
                    <el-radio-button :label=1 :value=1>是</el-radio-button>
                </el-radio-group>
            </el-form-item>


            <el-form-item label="视频合成秒数:" prop="second" v-if="form.second_infinite == 0">
                <el-input-number
                        v-model="form.second"
                        placeholder="请输入视频合成秒数"
                />
            </el-form-item>

            <el-form-item label="声音高保真次数:" prop="voice_twin_count">
                <el-input-number
                        v-model="form.voice_twin_count"
                        placeholder="请输入声音高保真次数"
                />
            </el-form-item>

            <el-form-item label="声音高保真合成字数:" prop="high_fidelity_words_number">
                <el-input-number
                        v-model="form.high_fidelity_words_number"
                        placeholder="声音高保真合成字数"
                />
            </el-form-item>

            <el-form-item label="ai文案仿写|提取次数:" prop="ai_copywriting_times">
                <el-input-number
                        v-model="form.ai_copywriting_times"
                        placeholder="ai文案合成次数"
                />
            </el-form-item>


            <el-form-item label="ai标题:" prop="ai_title_times">
                <el-input-number
                        v-model="form.ai_title_times"
                        placeholder="ai标题合成次数"
                />
            </el-form-item>


            <el-form-item label="专业版声音克隆次数:" prop="xunfei_sound_clone_words_number">
                <el-input-number
                        v-model="form.xunfei_sound_clone_words_number"
                        placeholder="专业版声音克隆次数"
                />
            </el-form-item>


            <el-form-item label="专业版声音合成字数:" prop="xunfei_fidelity_words_number">
                <el-input-number
                        v-model="form.xunfei_fidelity_words_number"
                        placeholder="专业版声音合成字数"
                />
            </el-form-item>


            <el-form-item label="排序号:" prop="sort">
                <el-input-number
                        :min="0"
                        v-model="form.sort"
                        placeholder="请输入排序号"
                        controls-position="right"
                        class="ele-fluid ele-text-left"
                />
            </el-form-item>

            <!-- <el-form-item label="推荐状态:">
              <el-switch
                :active-value="1"
                :inactive-value="2"
                v-model="form.is_recommend"
              />
              <el-tooltip
                placement="top"
                content="选择不推荐则前端页面不推荐该数据"
              >
                <i
                  class="el-icon-_question"
                  style="vertical-align: middle; margin-left: 8px"
                ></i>
              </el-tooltip>
            </el-form-item> -->

            <el-form-item label="展示状态:">
                <el-switch
                        :active-value="1"
                        :inactive-value="2"
                        v-model="form.is_display"
                />
                <el-tooltip
                        placement="top"
                        content="选择不可见则前端页面不显示该数据"
                >
                    <i
                            class="el-icon-_question"
                            style="vertical-align: middle; margin-left: 8px"
                    ></i>
                </el-tooltip>
            </el-form-item>

            <el-form-item label="权益描述:">
                <el-button
                    type="primary"
                    icon="el-icon-edit"
                    @click="openBenefitsEdit"
                >
                    编辑权益描述
                    <span v-if="benefitsCount > 0" class="benefits-count">({{ benefitsCount }}项)</span>
                </el-button>
                <el-tooltip
                    placement="top"
                    content="设置会员权益的详细描述信息，包括权益标题、图片和说明"
                >
                    <i
                        class="el-icon-_question"
                        style="vertical-align: middle; margin-left: 8px"
                    ></i>
                </el-tooltip>
            </el-form-item>

            <!-- 扣点配置区域 -->
            <el-divider content-position="left">
                <span style="font-size: 16px; font-weight: bold; color: #409EFF;">扣点配置</span>
            </el-divider>

            <!-- AI功能类字段 -->
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="AI立项:" prop="ai_project_deduct">
                        <el-input-number
                            v-model="form.ai_project_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="AI诊断分析:" prop="ai_diagnosis_deduct">
                        <el-input-number
                            v-model="form.ai_diagnosis_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="AI商业定位:" prop="ai_business_position_deduct">
                        <el-input-number
                            v-model="form.ai_business_position_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="AI账号包装:" prop="ai_account_package_deduct">
                        <el-input-number
                            v-model="form.ai_account_package_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="深度思考:" prop="deep_thinking_deduct">
                        <el-input-number
                            v-model="form.deep_thinking_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="爆款选题:" prop="hot_topic_deduct">
                        <el-input-number
                            v-model="form.hot_topic_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="文案编导:" prop="copywriting_director_deduct">
                        <el-input-number
                            v-model="form.copywriting_director_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="热点跟拍:" prop="hotspot_follow_deduct">
                        <el-input-number
                            v-model="form.hotspot_follow_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="小红书笔记:" prop="xiaohongshu_note_deduct">
                        <el-input-number
                            v-model="form.xiaohongshu_note_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="AI翻译官:" prop="ai_translator_deduct">
                        <el-input-number
                            v-model="form.ai_translator_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <!-- 视频编辑类字段 -->
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="口播精剪:" prop="voiceover_edit_deduct">
                        <el-input-number
                            v-model="form.voiceover_edit_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="批量混剪:" prop="batch_mix_edit_deduct">
                        <el-input-number
                            v-model="form.batch_mix_edit_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="图生视频720P:" prop="image_to_video_720p_deduct">
                        <el-input-number
                            v-model="form.image_to_video_720p_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="图生视频1080P:" prop="image_to_video_1080p_deduct">
                        <el-input-number
                            v-model="form.image_to_video_1080p_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="文生视频720P:" prop="text_to_video_720p_deduct">
                        <el-input-number
                            v-model="form.text_to_video_720p_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="文生视频1080P:" prop="text_to_video_1080p_deduct">
                        <el-input-number
                            v-model="form.text_to_video_1080p_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <!-- 图片设计类字段 -->
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="AI设计:" prop="ai_design_deduct">
                        <el-input-number
                            v-model="form.ai_design_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="图片精修:" prop="image_retouch_deduct">
                        <el-input-number
                            v-model="form.image_retouch_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="表情包:" prop="emoji_package_deduct">
                        <el-input-number
                            v-model="form.emoji_package_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="商品抠图:" prop="product_cutout_deduct">
                        <el-input-number
                            v-model="form.product_cutout_deduct"
                            :min="0"
                            placeholder="请输入"
                            controls-position="right"
                            class="ele-fluid"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <template v-slot:footer>
            <el-button @click="updateVisible(false)">取消</el-button>
            <el-button type="primary" :loading="loading" @click="save"
            >保存
            </el-button>
        </template>
        <uploadPictures
                :isChoice="isChoice"
                :visible.sync="modalPic"
                @getPic="getPic"
                :gridBtn="gridBtn"
                :gridPic="gridPic"
                :title="modalTitle"
        ></uploadPictures>

        <!-- 权益描述编辑弹窗 -->
        <member-benefits-edit
            :visible.sync="showBenefitsEdit"
            :value="form.member_info"
            @confirm="onBenefitsConfirm"
        />
    </ele-modal>
</template>

<script>
    import PagesSearch from "@/views/common/pages/pages-search";
    import uploadPictures from "@/components/uploadPictures";
    import EleImageUpload from 'ele-admin/es/ele-image-upload';
    import TinymceEditor from '@/components/TinymceEditor';
    import MemberBenefitsEdit from './member-benefits-edit';
    import {add, update} from '@/api/member/list';
    import {getPages} from '@/api/layout';

    const DEFAULT_FORM = {
        id: 0,
        name: '',
        price: '',
        money: '',
        point: 0,
        second_infinite: 0,
        second: 0,
        voice_twin_count: 0,
        day: 0,
        is_display: 1,
        is_recommend: 1,
        sort: 1,
        ai_copywriting_times: '',
        ai_title_times:'',
        high_fidelity_words_number: '',
        xunfei_sound_clone_words_number: 0,
        xunfei_fidelity_words_number:0,
        member_info: '', // 权益描述JSON字符串
        // 新增扣点配置字段
        ai_project_deduct: 0,
        ai_diagnosis_deduct: 0,
        ai_business_position_deduct: 0,
        ai_account_package_deduct: 0,
        deep_thinking_deduct: 0,
        hot_topic_deduct: 0,
        copywriting_director_deduct: 0,
        hotspot_follow_deduct: 0,
        xiaohongshu_note_deduct: 0,
        voiceover_edit_deduct: 0,
        batch_mix_edit_deduct: 0,
        ai_design_deduct: 0,
        image_retouch_deduct: 0,
        emoji_package_deduct: 0,
        product_cutout_deduct: 0,
        ai_translator_deduct: 0,
        image_to_video_720p_deduct: 0,
        image_to_video_1080p_deduct: 0,
        text_to_video_720p_deduct: 0,
        text_to_video_1080p_deduct: 0
    };

    export default {
        name: 'MemberEdit',
        components: {PagesSearch, EleImageUpload, uploadPictures, TinymceEditor, MemberBenefitsEdit},
        props: {
            // 弹窗是否打开
            visible: Boolean,
            // 修改回显的数据
            data: Object
        },
        computed: {
            balanceName() {
                return this.$store.state.user.balance_name;
            },
            // 权益描述数量
            benefitsCount() {
                if (!this.memberBenefits || !Array.isArray(this.memberBenefits)) {
                    return 0;
                }
                return this.memberBenefits.length;
            }
        },
        data() {
            return {
                modalTitle: '选择图片',
                modalPic: false,
                isChoice: "单选",
                gridBtn: {
                    xl: 4,
                    lg: 8,
                    md: 8,
                    sm: 8,
                    xs: 8,
                },
                gridPic: {
                    xl: 6,
                    lg: 8,
                    md: 12,
                    sm: 12,
                    xs: 12,
                },
                // 表单数据
                form: {...DEFAULT_FORM},
                // 表单验证规则
                rules: {
                    name: [
                        {
                            required: true,
                            message: '请输入会员名称',
                            trigger: 'blur'
                        }
                    ],
                    day: [
                        {
                            required: true,
                            message: '请输入会员天数',
                            trigger: 'blur'
                        }
                    ],
                    money: [
                        {
                            required: true,
                            message: '请输入会员价格',
                            trigger: 'blur'
                        }
                    ],
                    // 新增字段验证规则（非必填，但需要是数字）
                    ai_project_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    ai_diagnosis_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    ai_business_position_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    ai_account_package_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    deep_thinking_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    hot_topic_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    copywriting_director_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    hotspot_follow_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    xiaohongshu_note_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    voiceover_edit_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    batch_mix_edit_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    ai_design_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    image_retouch_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    emoji_package_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    product_cutout_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    ai_translator_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    image_to_video_720p_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    image_to_video_1080p_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    text_to_video_720p_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ],
                    text_to_video_1080p_deduct: [
                        { type: 'number', message: '请输入有效数字', trigger: 'blur' }
                    ]
                },
                // 提交状态
                loading: false,
                // 是否是修改
                isUpdate: false,
                // 权益描述相关
                showBenefitsEdit: false,
                memberBenefits: [],
                tableConfig: {
                    datasource: ({page, limit, where, order}) => {
                        return getPages({...where, ...order, page, limit});
                    },
                    columns: [
                        {
                            columnKey: 'index',
                            type: 'index',
                            width: 45,
                            align: 'center'
                        },
                        {
                            prop: 'name',
                            label: '名称',
                            showOverflowTooltip: true,
                            minWidth: 110,
                            //slot: 'nickname'
                        },
                        {
                            prop: 'url',
                            label: '路径',
                            showOverflowTooltip: true,
                            minWidth: 110
                        }
                    ],
                    rowClickChecked: true,
                    rowClickCheckedIntelligent: false,
                    toolkit: ['reload', 'columns'],
                    size: 'small',
                    toolStyle: {padding: '0 10px'}
                }
            };
        },
        methods: {
            updateSecond() {
                if (this.form.second_infinite == 1) {
                    this.form.second = 0;
                }

            },
            search(where) {
                // debugger
                this.$refs.select.reload({
                    where: where,
                    page: 1
                });
            },
            // 选择图片
            modalPicTap(tit, picTit, openTitle) {
                this.modalTitle = openTitle;
                this.isChoice = tit === "dan" ? "单选" : "多选";
                this.picTit = picTit;
                this.modalPic = true;
            },
            // 选中图片
            getPic(pc) {
                switch (this.picTit) {
                    case "pic_url":
                        this.form.pic_url = pc.satt_dir;
                        break;
                }
                this.modalPic = false;
            },
            //删除图片
            handleRemove() {
                this.form.pic_url = '';
            },

            // 打开权益描述编辑弹窗
            openBenefitsEdit() {
                this.showBenefitsEdit = true;
            },

            // 权益描述确认回调
            onBenefitsConfirm(benefits) {
                this.memberBenefits = benefits;
                // 将权益描述数组转换为JSON字符串存储到表单中
                this.form.member_info = JSON.stringify(benefits);
            },

            /* 保存编辑 */
            save() {
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }

                    this.loading = true;
                    const data = {
                        ...this.form,
                    };

                    const saveOrUpdata = this.isUpdate ? update : add;

                    saveOrUpdata(data).then((msg) => {
                        this.loading = false;
                        this.$message.success(msg);
                        this.updateVisible(false);
                        this.$emit('done');
                    }).catch((e) => {
                        this.loading = false;
                        this.$message.error(e.message);
                    });
                });
            },
            /* 更新visible */
            updateVisible(value) {
                this.$emit('update:visible', value);
            }
        },
        watch: {
            visible(visible) {
                if (visible) {
                    if (this.data) {
                        this.$util.assignObject(this.form, {
                            ...this.data
                        });
                        this.isUpdate = true;

                        // 处理权益描述数据回显
                        if (this.data.member_info) {
                            try {
                                // 如果member_info是字符串，解析为数组
                                if (typeof this.data.member_info === 'string') {
                                    this.memberBenefits = this.data.member_info ? JSON.parse(this.data.member_info) : [];
                                } else if (Array.isArray(this.data.member_info)) {
                                    this.memberBenefits = [...this.data.member_info];
                                } else {
                                    this.memberBenefits = [];
                                }
                                console.log('权益描述数据回显:', this.memberBenefits);
                            } catch (e) {
                                console.error('解析权益描述数据失败:', e, this.data.member_info);
                                this.memberBenefits = [];
                            }
                        } else {
                            this.memberBenefits = [];
                        }
                    } else {
                        this.isUpdate = false;
                        this.memberBenefits = [];
                    }
                } else {
                    this.$refs['form'].clearValidate();
                    this.form = {...DEFAULT_FORM};
                    this.memberBenefits = [];
                }
            }
        }
    };
</script>

<style scoped>
.benefits-count {
    color: #67c23a;
    font-weight: bold;
    margin-left: 5px;
}
</style>

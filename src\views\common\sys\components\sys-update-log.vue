<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">版本记录</div>
      <div class="ele-page-desc">用于管理系统版本更新日志。</div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <!-- 工具栏 -->
        <div style="margin-bottom: 15px;">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            @click="openEdit()"
          >
            新增版本记录
          </el-button>
          <el-button
            size="small"
            icon="el-icon-refresh"
            @click="reload()"
          >
            刷新
          </el-button>
        </div>

        <!-- 数据表格 -->
        <ele-pro-table
          ref="table"
          :columns="columns"
          :datasource="datasource"
          :selection.sync="selection"
          :pagination="{ pageSize: 20 }"
        >
          <!-- 类型列 -->
          <template v-slot:type="{ row }">
            <el-tag
              :type="getTypeTagType(row.type)"
              size="small"
            >
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>

          <!-- 更新内容列 -->
          <template v-slot:content="{ row }">
            <div style="max-width: 300px; word-break: break-all; line-height: 1.4;">
              {{ row.content }}
            </div>
          </template>

          <!-- 版本号列 -->
          <template v-slot:version="{ row }">
            <el-tag size="mini" type="info">{{ row.version }}</el-tag>
          </template>

          <!-- 操作列 -->
          <template v-slot:action="{ row }">
            <el-link
              type="primary"
              :underline="false"
              icon="el-icon-edit"
              @click="openEdit(row)"
            >
              修改
            </el-link>
            <el-divider direction="vertical"></el-divider>
            <el-link
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click="remove(row)"
            >
              删除
            </el-link>
          </template>
        </ele-pro-table>
      </el-card>
    </div>

    <!-- 编辑弹窗 -->
    <update-log-edit
      :visible.sync="showEdit"
      :data="current"
      @done="reload"
    />
  </div>
</template>

<script>
import { list, remove } from '@/api/system/update-log';
import UpdateLogEdit from './update-log-edit';

export default {
  name: 'SysUpdateLog',
  components: {
    UpdateLogEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          prop: 'type',
          label: '类型',
          align: 'center',
          width: 100,
          slot: 'type'
        },
        {
          prop: 'version',
          label: '版本号',
          align: 'center',
          width: 120,
          slot: 'version'
        },
        {
          prop: 'content',
          label: '更新内容',
          minWidth: 200,
          slot: 'content'
        },
        {
          prop: 'sys_update_time',
          label: '更新时间',
          align: 'center',
          width: 120
        },
        {
          prop: 'create_time',
          label: '创建时间',
          align: 'center',
          width: 150
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 150,
          align: 'center',
          resizable: false,
          slot: 'action'
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false
    };
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      console.log('版本记录列表请求参数:', { page, size: limit, ...where, ...order });
      return list({ page, size: limit, ...where, ...order }).then(res => {
        console.log('版本记录列表返回数据:', res);
        return res;
      }).catch(err => {
        console.error('版本记录列表请求错误:', err);
        throw err;
      });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },
    /* 删除 */
    remove(row) {
      this.$confirm('确定要删除此版本记录吗?', '提示', {
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      }).catch(() => {});
    },
    /* 获取类型文字 */
    getTypeText(type) {
      const typeMap = {
        1: '重大更新',
        2: '功能更新',
        3: '问题修复'
      };
      return typeMap[type] || '未知';
    },
    /* 获取类型标签样式 */
    getTypeTagType(type) {
      const typeMap = {
        1: 'danger',
        2: 'primary',
        3: 'success'
      };
      return typeMap[type] || '';
    }
  }
};
</script>

<style scoped>
</style>

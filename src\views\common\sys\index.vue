<template>
  <div class="ele-body">
    <el-card shadow="never" body-style="padding: 0;">
      <div class="ele-cell ele-cell-align-top ele-user-message">
        <el-menu
          :mode="mode"
          :default-active="active"
          class="ele-scrollbar-hide"
        >
         <el-menu-item index="base">
            <router-link to="/common/sys?type=base">
              <el-badge class="ele-badge-static"/>
              <span>基础配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="system">
            <router-link to="/common/sys?type=system">
              <el-badge class="ele-badge-static"/>
              <span>平台配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="upload">
            <router-link to="/common/sys?type=upload">
              <el-badge class="ele-badge-static"/>
              <span>上传配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="pay">
            <router-link to="/common/sys?type=pay">
              <el-badge class="ele-badge-static"/>
              <span>支付配置</span>
            </router-link>
          </el-menu-item>

          <!-- <el-menu-item index="note">
            <router-link to="/common/sys?type=note">
              <el-badge class="ele-badge-static"/>
              <span>短信配置</span>
            </router-link>
          </el-menu-item> -->

          <el-menu-item index="forward">
            <router-link to="/common/sys?type=forward">
              <el-badge class="ele-badge-static"/>
              <span>转发配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="template">
            <router-link to="/common/sys?type=template">
              <el-badge class="ele-badge-static"/>
              <span>通知配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="tally">
            <router-link to="/common/sys?type=tally">
              <el-badge class="ele-badge-static"/>
              <span>扣点配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="account">
            <router-link to="/common/sys?type=account">
              <el-badge class="ele-badge-static"/>
              <span>账号配置</span>
            </router-link>
          </el-menu-item>

           <el-menu-item index="customer">
            <router-link to="/common/sys?type=customer">
              <el-badge class="ele-badge-static"/>
              <span>客服配置</span>
            </router-link>
          </el-menu-item>

          <el-menu-item index="timer">
            <router-link to="/common/sys?type=timer">
              <el-badge class="ele-badge-static"/>
              <span>定时任务</span>
            </router-link>
          </el-menu-item>

        </el-menu>
        
        <div class="ele-cell-content" style="overflow-x: hidden">
          <transition name="slide-right" mode="out-in">
            <sys-base v-if="active === 'base'"/>
            <sys-system v-else-if="active === 'system'"/>
            <sys-upload v-else-if="active === 'upload'"/>
            <sys-pay v-else-if="active === 'pay'" />
            <sys-note v-else-if="active === 'note'"/>
            <sys-account v-else-if="active === 'account'"/>
            <sys-template v-else-if="active === 'template'"/>
            <sys-timer v-else-if="active === 'timer'"/>
            <sys-tally v-else-if="active === 'tally'"/>
             <sys-customer v-else-if="active === 'customer'"/>
            <sys-forward v-else-if="active === 'forward'"/>
          </transition>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
  import SysBase from './components/sys-base.vue';
  import SysSystem from './components/sys-system.vue';
  import SysUpload from './components/sys-upload.vue';
  import SysPay from './components/sys-pay.vue';
  import SysTemplate from './components/sys-template.vue';
  import SysNote from './components/sys-note.vue';
  import SysAccount from './components/sys-account.vue';
  import SysTimer from './components/sys-timer.vue';
  import SysTally from './components/sys-tally.vue';
  import SysForward from './components/sys-forward.vue';
  import SysCustomer from './components/sys-customer.vue';
   
  export default {
    name: 'SysConfig',
    components: { SysBase,SysSystem,SysUpload,SysPay,SysTemplate,SysAccount,SysNote,SysTimer,SysTally,SysForward,SysCustomer},
    data() {
      return {
        // 导航选中
        active: 'base',
        // 通知未读数量
        unReadNotice: 0,
        // 私信未读数量
        unReadLetter: 0,
        // 代办未读数量
        unReadTodo: 0
      };
    },
    computed: {
      // 小屏幕水平导航
      mode() {
        return this.$store.state.theme.screenWidth < 768
          ? 'horizontal'
          : 'vertical';
      }
    },
    watch: {
      $route: {
        handler(route) {
          if (route.path === '/common/sys') {
            if(route?.query?.type){
              //let arr = this.queryUnReadNum();
              this.active = route?.query?.type;
            }
          }
        },
        immediate: true
      }
    },
    created() {
      this.queryUnReadNum();
    },
    methods: {
      /* 查询未读数量 */
      queryUnReadNum() {
        
      }
    }
  };
</script>

<style lang="scss" scoped>
  .ele-user-message ::v-deep .el-menu {
    width: 151px;
    flex-shrink: 0;
  }

  .ele-user-message .ele-cell-content {
    padding: 15px;
    box-sizing: border-box;
    overflow: auto;
  }

  .ele-user-message .ele-badge-static {
    margin-right: 10px;
  }

  @media screen and (min-width: 768px) {
    .ele-user-message ::v-deep .el-menu {
      .el-menu-item {
        text-align: right;
      }

      .el-menu-item:first-child {
        margin-top: 15px;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .ele-user-message {
      display: block;
    }

    .ele-user-message ::v-deep .el-menu {
      width: auto;
      text-align: center;
      white-space: nowrap;
      overflow: auto;

      .el-menu-item {
        height: 45px;
        line-height: 45px;
        padding: 0 5px;
        display: inline-block;
        float: none;
      }
    }

    .ele-user-message .ele-badge-static {
      margin-left: 3px;
    }
  }
</style>
